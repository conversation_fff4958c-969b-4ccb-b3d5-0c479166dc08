package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson.JSON;
import com.unipus.digitalbook.common.exception.knowledge.KnowledgeResourceExistException;
import com.unipus.digitalbook.common.exception.knowledge.KnowledgeResourceUrlNullException;
import com.unipus.digitalbook.dao.*;
import com.unipus.digitalbook.model.dto.knowledge.BookKnowledgeSourceInfoDTO;
import com.unipus.digitalbook.model.dto.knowledge.BookKnowledgeSourceInfoGroupDTO;
import com.unipus.digitalbook.model.dto.knowledge.KnowledgeSourceCheckDTO;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.enums.KnowledgeResourceEnableEnum;
import com.unipus.digitalbook.model.enums.KnowledgeResourceTypeEnum;
import com.unipus.digitalbook.model.enums.KnowledgeSourceDeleteEnum;
import com.unipus.digitalbook.model.enums.KnowledgeSourceMainEnum;
import com.unipus.digitalbook.model.params.knowledge.*;
import com.unipus.digitalbook.model.po.knowledge.*;
import com.unipus.digitalbook.service.KnowledgeResourceService;
import com.unipus.digitalbook.service.UserService;
import com.unipus.digitalbook.service.remote.restful.knowledge.KnowledgeApiService;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeResourceAddRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeResourceListDetailRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.request.KnowledgeResourceUpdateRequest;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.BaseKnowledgeResponse;
import com.unipus.digitalbook.service.remote.restful.knowledge.model.response.KnowledgeResourceDetailResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 图谱资源创建相关接口
 */
@Service
@Slf4j
public class KnowledgeResourceServiceImpl implements KnowledgeResourceService {

    @Resource
    KnowledgeApiService knowledgeApiService;

    @Resource
    BookKnowledgeSourceInfoMapper bookKnowledgeSourceInfoMapper;

    @Resource
    BookKnowledgeResourceInfoMapper bookKnowledgeResourceInfoMapper;
    @Resource
    BookKnowledgeResourceDetailInfoMapper bookKnowledgeResourceDetailInfoMapper;
    @Resource
    BookKnowledgeResourceMediaInfoMapper bookKnowledgeResourceMediaInfoMapper;
    @Resource
    BookKnowledgeResourceQuestionInfoMapper bookKnowledgeResourceQuestionInfoMapper;

    @Resource
    UserService userService;

    @Value("${remote.knowledge.resourceLinkUrl}")
    private String sourceLinkBaseUrl;

    private String sourceLinkUrlStr = "%s?path=%s&primaryKey=%s&source=%s";

    @Override
    public String knowledgeSourceAdd(KnowledgeSourceAddParam params, Long opUserId) {
        //校验待处理的url是否已经标注，如果标注则抛出存在异常～
        List<String> pendingUrls = new ArrayList<>();
        pendingUrls.add(params.getSourceUrl());
        if (!CollectionUtils.isEmpty(params.getSubSourceUrls())) {
            pendingUrls.addAll(params.getSubSourceUrls());
        }
        checkUrlsExistThenThrow(pendingUrls, params.getBooId(),
                params.getChapterId(), params.getType(), params.getBookKnowledgeId(), params.getKnowledgeId(), null);

        /**
         * 1、调用三方接口，上传资源，生成资源Id
         * 2、添加知识图谱节点数据
         */
        String sourceUrl = String.format(sourceLinkUrlStr, sourceLinkBaseUrl, params.getSourceUrl(), params.getBooId(), params.getSource());
        KnowledgeResourceAddRequest knowledgeResourceAddRequest = params.toThirdKnowledgeAddRequest(sourceUrl);
        log.info("third request : {}", JSON.toJSONString(knowledgeResourceAddRequest));
        //图谱返回的资源Id
        BaseKnowledgeResponse<String> knowledgeResourceId = knowledgeApiService.addResource(knowledgeResourceAddRequest);
        BookKnowledgeResourceCompleteInfoPO mainSourceInfoPO = params.toPo(knowledgeResourceId.getResult(), opUserId);

        bookKnowledgeSourceInfoMapper.insertSelective(mainSourceInfoPO);

        //如果打标是组，则生成子数据
        if (params.groupFlag()) {
            List<BookKnowledgeResourceCompleteInfoPO> subPoList = params.toSubPo(mainSourceInfoPO);
            bookKnowledgeSourceInfoMapper.batchInsertSelective(subPoList);
        }
        return mainSourceInfoPO.getId() + "";
    }

    @Override
    public void knowledgeSourceUpdate(KnowledgeSourceUpdateParam params, Long opUserId) {

        KnowledgeResourceUpdateRequest updateRequest = params.toThirdKnowledgeResourceUpdateRequest();
        log.info("third request : {}", JSON.toJSONString(updateRequest));
        knowledgeApiService.updateResource(updateRequest);

        //更新本地数据
        List<BookKnowledgeResourceCompleteInfoPO> pendingUpdateList = params.toPo(opUserId);
        for (BookKnowledgeResourceCompleteInfoPO updatePO : pendingUpdateList) {
            bookKnowledgeSourceInfoMapper.updateAllInfoByPrimaryKeySelective(updatePO);
        }
    }

    @Override
    public void knowledgeSourceDisable(KnowledgeSourceIdParam param, Long opUserId) {
        BookKnowledgeResourceCompleteInfoPO sourceInfoPO = new BookKnowledgeResourceCompleteInfoPO();
        sourceInfoPO.setId(param.getId());
        sourceInfoPO.setEnable(KnowledgeResourceEnableEnum.DISABLE.getCode());
        sourceInfoPO.setUpdateBy(opUserId);
        sourceInfoPO.setUpdateTime(new Date());
        bookKnowledgeSourceInfoMapper.updateEnableStatusAllSourceInfoById(sourceInfoPO);
    }

    @Override
    public void knowledgeSourceDirUpdate(KnowledgeSourceDirUpdateParam param, Long opUserId) {
        KnowledgeResourceUpdateRequest updateRequest = param.toThirdKnowledgeResourceUpdateRequest();
        log.info("third request : {}", JSON.toJSONString(updateRequest));
        knowledgeApiService.updateResource(updateRequest);

        BookKnowledgeResourceCompleteInfoPO mainPo = param.toPo(opUserId);
        bookKnowledgeSourceInfoMapper.updateDirAllSourceInfoById(mainPo);
    }

    @Override
    public void knowledgeSourceDelete(KnowledgeSourceDeleteParam params, Long opUserId) {
        //删除三方资源Id
        knowledgeApiService.deleteResource(params.getThirdResourceId(), params.getKnowledgeId());

        BookKnowledgeResourceCompleteInfoPO sourceInfoPO = new BookKnowledgeResourceCompleteInfoPO();
        sourceInfoPO.setId(params.getCourseResourceId());
        sourceInfoPO.setDeleteStatus(KnowledgeSourceDeleteEnum.DELETE.getCode());
        sourceInfoPO.setUpdateBy(opUserId);
        sourceInfoPO.setUpdateTime(new Date());
        //删除跟主键Id的本地所有资源
        bookKnowledgeSourceInfoMapper.deleteAllSourceInfoById(sourceInfoPO);
    }

    @Override
    public void knowledgeSourceBatchDelete(KnowledgeSourceBatchDeleteParam param, Long opUserId) {
        List<KnowledgeSourceBatchDeleteParam.BatchIds> pendingDeleteIdInfos = param.getDeleteIds();
        for (KnowledgeSourceBatchDeleteParam.BatchIds pendingDeleteIdInfo : pendingDeleteIdInfos) {
            if (null == pendingDeleteIdInfo.getCourseResourceId() || StringUtils.isEmpty(pendingDeleteIdInfo.getThirdResourceId())) {
                continue;
            }
            KnowledgeSourceDeleteParam deleteParam = new KnowledgeSourceDeleteParam();
            deleteParam.setKnowledgeId(param.getKnowledgeId());
            deleteParam.setCourseResourceId(pendingDeleteIdInfo.getCourseResourceId());
            deleteParam.setThirdResourceId(pendingDeleteIdInfo.getThirdResourceId());
            this.knowledgeSourceDelete(deleteParam, opUserId);
        }
    }

    @Override
    public void knowledgeSourceDeleteByThirdIds(KnowledgeSourceDeleteByThirdIdsParam param, Long opUserId) {
        log.warn("知识点被删除，级联下方的所有资源被删除～");
        bookKnowledgeSourceInfoMapper.deleteAllSourceInfoByThirdIds(param.getThirdResourceIds(), opUserId, param.getKnowledgeId());
    }

    @Override
    public List<BookKnowledgeSourceInfoDTO> knowledgeSourceQuery(KnowledgeSourceQueryParam param, Long opUserId) {
        List<BookKnowledgeSourceInfoDTO> result = new ArrayList<>();

        BookKnowledgeResourceCompleteInfoPO queryPO = param.toQueryPO();
        List<BookKnowledgeResourceCompleteInfoPO> infoPOList = bookKnowledgeSourceInfoMapper.selectSelective(queryPO);
        if (CollectionUtils.isEmpty(infoPOList)) {
            //根据条件没有查到资源数据,直接返回
            return result;
        }

        List<BookKnowledgeResourceCompleteInfoPO> mainInfoList = new ArrayList<>();
        List<BookKnowledgeResourceCompleteInfoPO> subInfoList = new ArrayList<>();
        Set<String> knowledgeSourceIdSet = new HashSet<>();

        for (BookKnowledgeResourceCompleteInfoPO sourceInfoPO : infoPOList) {
            if (null == sourceInfoPO.getParentId()) {
                mainInfoList.add(sourceInfoPO);
                knowledgeSourceIdSet.add(sourceInfoPO.getKnowledgeSourceId());
            } else {
                subInfoList.add(sourceInfoPO);
            }
        }

        if (knowledgeSourceIdSet.isEmpty()) {
            log.warn("三方资源Id为空，数据不正常，请排查数据");
            //暂时没有资源生成,直接返回
            return result;
        }
        //组内子资源map
        Map<Long, List<BookKnowledgeResourceCompleteInfoPO>> subInfoMap = new HashMap<>();
        if (!subInfoList.isEmpty()) {
            //子资源map数据
            subInfoMap =
                    subInfoList.stream().collect(Collectors.groupingBy(BookKnowledgeResourceCompleteInfoPO::getParentId));
        }

        //三方获取知识资源信息
        Map<String, KnowledgeResourceDetailResponse> resourceListDetailMap = getKnowledgeResourceDetailResponseMap(knowledgeSourceIdSet);

        //组装返回值
        for (BookKnowledgeResourceCompleteInfoPO sourceInfoPO : mainInfoList) {
            Long lastUpdateUserId = null;
            //云知声返回的图谱资源Id
            String knowledgeSourceId = sourceInfoPO.getKnowledgeSourceId();
            if (!resourceListDetailMap.containsKey(knowledgeSourceId)) {
                log.warn("knowledgeSourceId is not exist in third platform:{},courseKnowledgeId:{}", knowledgeSourceId, sourceInfoPO.getId());
                continue;
            }
            KnowledgeResourceDetailResponse resourceDetailResponse = resourceListDetailMap.get(knowledgeSourceId);

            BookKnowledgeSourceInfoDTO sourceInfoDTO = new BookKnowledgeSourceInfoDTO();
            sourceInfoDTO.setCourseKnowledgeId(sourceInfoPO.getBookKnowledgeId());
            sourceInfoDTO.setCourseResourceId(sourceInfoPO.getId());
            sourceInfoDTO.setThirdResourceId(resourceDetailResponse.getId());
            sourceInfoDTO.setName(resourceDetailResponse.getName());
            sourceInfoDTO.setType(sourceInfoPO.getType() + "");
            sourceInfoDTO.setUrl(resourceDetailResponse.getUrl());
            sourceInfoDTO.setDir(resourceDetailResponse.getDir());
            sourceInfoDTO.setDescription(resourceDetailResponse.getDescription());
            sourceInfoDTO.setNodes(resourceDetailResponse.getNodes());
            sourceInfoDTO.setLabels(resourceDetailResponse.getLabels());
            sourceInfoDTO.setMainSourceUrl(sourceInfoPO.getSourceUrl());
            sourceInfoDTO.setEnableStatus(sourceInfoPO.getEnable());
            sourceInfoDTO.setStartTime(sourceInfoPO.getStartTime());
            sourceInfoDTO.setStartPictureUrl(sourceInfoPO.getStartPictureUrl());
            sourceInfoDTO.setCreateTime(sourceInfoPO.getCreateTime().getTime());
            sourceInfoDTO.setUpdateTime(sourceInfoPO.getUpdateTime().getTime());
            sourceInfoDTO.setMultimediaKey(sourceInfoPO.getMultimediaKey());
            sourceInfoDTO.setMultimediaIndex(sourceInfoPO.getMultimediaIndex());
            sourceInfoDTO.setMultimediaName(sourceInfoPO.getMultimediaName());
            sourceInfoDTO.setLocation(sourceInfoPO.getLocation());
            lastUpdateUserId = sourceInfoPO.getUpdateBy();

            //如果是组的话，组内资源也需要组装
            if (subInfoMap.containsKey(sourceInfoPO.getId())) {
                List<BookKnowledgeResourceCompleteInfoPO> groupInnerInfo = subInfoMap.get(sourceInfoPO.getId());
                List<BookKnowledgeSourceInfoDTO.SubSourceInfo> subSourceInfos = new ArrayList<>();
                sourceInfoDTO.setSubSourceList(subSourceInfos);
                for (BookKnowledgeResourceCompleteInfoPO innerInfo : groupInnerInfo) {
                    BookKnowledgeSourceInfoDTO.SubSourceInfo subSourceInfo = new BookKnowledgeSourceInfoDTO.SubSourceInfo();
                    subSourceInfo.setId(innerInfo.getId());
                    subSourceInfo.setSourceUrl(innerInfo.getSourceUrl());
                    subSourceInfo.setEnableStatus(innerInfo.getEnable());
                    if (innerInfo.getUpdateTime().getTime() > sourceInfoPO.getUpdateTime().getTime()) {
                        lastUpdateUserId = innerInfo.getUpdateBy();
                    }
                    subSourceInfos.add(subSourceInfo);
                }
            }

            //设置最后更新人
            UserInfo userInfo = userService.getUserInfo(lastUpdateUserId);
            sourceInfoDTO.setUserName(userInfo.getName());
            sourceInfoDTO.setAvatarUrl(userInfo.getAvatarUrl());
            sourceInfoDTO.setSsoId(userInfo.getSsoId());
            //设置数据
            result.add(sourceInfoDTO);
        }

        return result;
    }

    @Override
    public List<BookKnowledgeSourceInfoGroupDTO> knowledgeSourceQueryGroup(KnowledgeSourceQueryGroupParam param, Long opUserId) {
        List<BookKnowledgeSourceInfoGroupDTO> result = new ArrayList<>();

        KnowledgeSourceQueryParam queryParam = new KnowledgeSourceQueryParam();
        BeanUtils.copyProperties(param, queryParam);
        List<BookKnowledgeSourceInfoDTO> bookKnowledgeSourceInfoDTOS = this.knowledgeSourceQuery(queryParam, opUserId);
        Map<String, List<BookKnowledgeSourceInfoDTO>> groupInfoByTypeMap = bookKnowledgeSourceInfoDTOS.stream().collect(Collectors.groupingBy(BookKnowledgeSourceInfoDTO::getMultimediaKey));
        if (CollectionUtils.isEmpty(groupInfoByTypeMap)) {
            return result;
        }

        groupInfoByTypeMap.forEach((multimediaKey, groupInfoList) -> {
            List<BookKnowledgeSourceInfoDTO> sortedList = groupInfoList.stream().sorted(Comparator.comparing(BookKnowledgeSourceInfoDTO::getMultimediaIndex)).collect(Collectors.toList());
            BookKnowledgeSourceInfoDTO lastInfo = sortedList.stream().max(Comparator.comparing(BookKnowledgeSourceInfoDTO::getUpdateTime)).get();
            BookKnowledgeSourceInfoGroupDTO groupDTO = new BookKnowledgeSourceInfoGroupDTO();
            groupDTO.setUserName(lastInfo.getUserName());
            groupDTO.setAvatarUrl(lastInfo.getAvatarUrl());
            groupDTO.setCreateTime(lastInfo.getCreateTime());
            groupDTO.setDir(lastInfo.getDir());
            groupDTO.setMultimediaKey(multimediaKey);
            groupDTO.setType(lastInfo.getType());
            groupDTO.setBookKnowledgeSourceInfoDTOS(sortedList);
            groupDTO.setLocation(lastInfo.getLocation());
            result.add(groupDTO);
        });

        //根据文章的绝对的位置，做最后的排序
        result.sort(Comparator.comparing(BookKnowledgeSourceInfoGroupDTO::getLocation));

        return result;
    }

    @Override
    public KnowledgeSourceCheckDTO check(KnowledgeSourceCheckParam param, Long opUserId) {

        List<BookKnowledgeResourceCompleteInfoPO> existSourceUrls = checkUrlsExistThenReturnInfos(param.getPendingUrls(), param.getBooId(),
                param.getChapterId(), param.getType(), param.getBookKnowledgeId(), param.getKnowledgeId(), param.getIgnoreIds());

        KnowledgeSourceCheckDTO result = new KnowledgeSourceCheckDTO();
        if (CollectionUtils.isEmpty(existSourceUrls)) {
            return result;
        }
        //有存在的url，不可再次添加
        result.setEffectiveStatus(false);
        result.setInfoList(existSourceUrls);
        return result;
    }

    @Override
    public KnowledgeSourceCheckDTO newCheck(KnowledgeSourceCheckParam param, Long opUserId) {

        List<BookKnowledgeResourceDetailInfoPO> existSourceUrls = checkUrlsNewExistThenReturnInfos(param.getPendingUrls(), param.getBooId(),
                param.getChapterId(), param.getKnowledgeId(), param.getType(), param.getBookKnowledgeId(), param.getIgnoreIds());

        KnowledgeSourceCheckDTO result = new KnowledgeSourceCheckDTO();
        if (CollectionUtils.isEmpty(existSourceUrls)) {
            return result;
        }
        //获取第一个已经存在的字段
        BookKnowledgeResourceDetailInfoPO detailInfo = existSourceUrls.getFirst();

        //返回必要信息
        BookKnowledgeResourceCompleteInfoPO completeInfoPO = new BookKnowledgeResourceCompleteInfoPO();
        completeInfoPO.setId(detailInfo.getResourceId());
        completeInfoPO.setSourceUrl(detailInfo.getSourceUrl());
        completeInfoPO.setBookId(detailInfo.getBookId());
        completeInfoPO.setChapterId(detailInfo.getChapterId());
        completeInfoPO.setLocation(detailInfo.getLocation());

        //有存在的url，不可再次添加
        result.setEffectiveStatus(false);
        result.setInfoList(Arrays.asList(completeInfoPO));
        return result;
    }


    @Transactional
    @Override
    public String knowledgeResourceAdd(KnowledgeSourceAddParam params, Long opUserId) {
        //校验待处理的url是否已经标注，如果标注则抛出存在异常～
        List<String> pendingUrls = new ArrayList<>();
        pendingUrls.add(params.getSourceUrl());
        if (!CollectionUtils.isEmpty(params.getSubSourceUrls())) {
            pendingUrls.addAll(params.getSubSourceUrls());
        }
        checkUrlsNewExistThenThrow(pendingUrls, params.getBooId(),
                params.getChapterId(), params.getKnowledgeId(), params.getType(), params.getBookKnowledgeId(), null);

        /**
         * 1、调用三方接口，上传资源，生成资源Id
         * 2、添加知识图谱节点数据
         */
        String sourceUrl = String.format(sourceLinkUrlStr, sourceLinkBaseUrl, params.getSourceUrl(), params.getBooId(), params.getSource());
        KnowledgeResourceAddRequest knowledgeResourceAddRequest = params.toThirdKnowledgeAddRequest(sourceUrl);
        log.info("third request : {}", JSON.toJSONString(knowledgeResourceAddRequest));
        //图谱返回的资源Id
        BaseKnowledgeResponse<String> knowledgeResourceId = knowledgeApiService.addResource(knowledgeResourceAddRequest);

        BookKnowledgeResourceInfoPO resourceInfoPO = params.toBookKnowledgeResourceInfoPO(knowledgeResourceId.getResult(), opUserId);
        //关联关系添加
        bookKnowledgeResourceInfoMapper.insertSelective(resourceInfoPO);
        List<BookKnowledgeResourceDetailInfoPO> detailInfoPOs = params.toBookKnowledgeResourceDetailInfoPOs(resourceInfoPO.getId(), opUserId, pendingUrls);
        bookKnowledgeResourceDetailInfoMapper.batchInsertSelective(detailInfoPOs);
        //补充视频附属参数
        if (Objects.equals(KnowledgeResourceTypeEnum.VIDEO.getCode(), params.getType())) {
            BookKnowledgeResourceMediaInfoPO mediaInfoPO = new BookKnowledgeResourceMediaInfoPO();
            mediaInfoPO.setResourceId(resourceInfoPO.getId());
            mediaInfoPO.setResourceDetailId(detailInfoPOs.getFirst().getId());
            mediaInfoPO.setStartTime(params.getStartTime());
            mediaInfoPO.setStartPictureUrl(params.getStartPictureUrl());
            mediaInfoPO.setMultimediaKey(params.getMultimediaKey());
            mediaInfoPO.setMultimediaIndex(params.getMultimediaIndex());
            mediaInfoPO.setEndTime(params.getEndTime());
            mediaInfoPO.setCreateTime(new Date());
            mediaInfoPO.setUpdateTime(new Date());
            mediaInfoPO.setCreateBy(opUserId);
            mediaInfoPO.setUpdateBy(opUserId);
            bookKnowledgeResourceMediaInfoMapper.insertSelective(mediaInfoPO);
        }
        //补充题目附属参数
        if (Objects.equals(KnowledgeResourceTypeEnum.QUESTION.getCode(), params.getType())) {
            BookKnowledgeResourceQuestionInfoPO questionInfoPO = new BookKnowledgeResourceQuestionInfoPO();
            questionInfoPO.setResourceId(resourceInfoPO.getId());
            questionInfoPO.setResourceDetailId(detailInfoPOs.getFirst().getId());
            questionInfoPO.setQuestionId(params.getMultimediaName());
            questionInfoPO.setQuestionIndex(params.getMultimediaIndex());
            questionInfoPO.setQuestionKey(params.getMultimediaKey());
            questionInfoPO.setCreateTime(new Date());
            questionInfoPO.setUpdateTime(new Date());
            questionInfoPO.setCreateBy(opUserId);
            questionInfoPO.setUpdateBy(opUserId);

            bookKnowledgeResourceQuestionInfoMapper.insertSelective(questionInfoPO);
        }

        return resourceInfoPO.getId() + "";
    }

    @Transactional
    @Override
    public void knowledgeResourceDelete(KnowledgeSourceDeleteParam param, Long opUserId) {
        //删除三方资源Id
        knowledgeApiService.deleteResource(param.getThirdResourceId(), param.getKnowledgeId());
        BookKnowledgeResourceInfoPO resourceInfoById = bookKnowledgeResourceInfoMapper.selectByPrimaryKey(param.getCourseResourceId());
        if (resourceInfoById == null) {
            log.warn("当前根据主键要修改的信息不存在");
            return;
        }

        resourceAllInfoDisableByResourceId(resourceInfoById.getId(), opUserId, resourceInfoById.getType());
    }

    @Transactional
    @Override
    public void knowledgeResourceBatchDelete(KnowledgeSourceBatchDeleteParam param, Long opUserId) {
        List<KnowledgeSourceBatchDeleteParam.BatchIds> pendingDeleteIdInfos = param.getDeleteIds();
        for (KnowledgeSourceBatchDeleteParam.BatchIds pendingDeleteIdInfo : pendingDeleteIdInfos) {
            if (null == pendingDeleteIdInfo.getCourseResourceId() || StringUtils.isEmpty(pendingDeleteIdInfo.getThirdResourceId())) {
                continue;
            }
            KnowledgeSourceDeleteParam deleteParam = new KnowledgeSourceDeleteParam();
            deleteParam.setKnowledgeId(param.getKnowledgeId());
            deleteParam.setCourseResourceId(pendingDeleteIdInfo.getCourseResourceId());
            deleteParam.setThirdResourceId(pendingDeleteIdInfo.getThirdResourceId());
            this.knowledgeResourceDelete(deleteParam, opUserId);
        }

    }

    @Override
    public void knowledgeResourceUpdate(KnowledgeSourceUpdateParam param, Long opUserId) {

        KnowledgeResourceUpdateRequest updateRequest = param.toThirdKnowledgeResourceUpdateRequest();
        log.info("third request : {}", JSON.toJSONString(updateRequest));
        knowledgeApiService.updateResource(updateRequest);
        BookKnowledgeResourceDetailInfoPO updateInfo = new BookKnowledgeResourceDetailInfoPO();
        updateInfo.setResourceId(param.getId());
        updateInfo.setUpdateTime(new Date());
        updateInfo.setUpdateBy(opUserId);
        bookKnowledgeResourceDetailInfoMapper.updateByResourceIdSelective(updateInfo);
    }

    @Transactional
    @Override
    public void knowledgeResourceDeleteByThirdIds(KnowledgeSourceDeleteByThirdIdsParam param, Long opUserId) {
        log.warn("知识点被删除，级联下方的所有资源被删除～");
        bookKnowledgeSourceInfoMapper.deleteAllSourceInfoByThirdIds(param.getThirdResourceIds(), opUserId, param.getKnowledgeId());
        if (CollectionUtils.isEmpty(param.getThirdResourceIds())) {
            log.info("三方的resourceId为空");
            return;
        }
        List<BookKnowledgeResourceInfoPO> bookKnowledgeResourceInfoPOS = bookKnowledgeResourceInfoMapper.selectListByThirdResourceIds(param.getThirdResourceIds(), param.getKnowledgeId());
        if (CollectionUtils.isEmpty(bookKnowledgeResourceInfoPOS)) {
            log.info("三方的resourceId已经被全部修改为失效了");
            return;
        }

        for (BookKnowledgeResourceInfoPO info : bookKnowledgeResourceInfoPOS) {
            resourceAllInfoDisableByResourceId(info.getId(), opUserId, info.getType());
        }
    }

    @Override
    public List<BookKnowledgeSourceInfoDTO> knowledgeResourceQuery(KnowledgeSourceQueryParam param, Long opUserId) {
        List<BookKnowledgeSourceInfoDTO> result = new ArrayList<>();

        BookKnowledgeResourceDetailInfoPO detailQuery = new BookKnowledgeResourceDetailInfoPO();
        detailQuery.setBookId(param.getBookId());
        detailQuery.setChapterId(param.getChapterId());
        detailQuery.setKnowledgeId(param.getKnowledgeId());
        detailQuery.setType(param.getType());
        detailQuery.setEnable(KnowledgeResourceEnableEnum.ENABLE.getCode());
        List<BookKnowledgeResourceDetailInfoPO> detailInfoPOS = bookKnowledgeResourceDetailInfoMapper.selectBySelective(detailQuery);
        if (CollectionUtils.isEmpty(detailInfoPOS)) {
            //根据条件没有查到资源数据,直接返回
            return result;
        }

        //处理资源数据
        List<BookKnowledgeResourceCompleteInfoPO> infoPOList = getBookKnowledgeResourceCompleteInfoPOS(param, detailInfoPOS);

        List<BookKnowledgeResourceCompleteInfoPO> mainInfoList = new ArrayList<>();
        List<BookKnowledgeResourceCompleteInfoPO> subInfoList = new ArrayList<>();
        Set<String> knowledgeSourceIdSet = new HashSet<>();

        for (BookKnowledgeResourceCompleteInfoPO sourceInfoPO : infoPOList) {
            if (null == sourceInfoPO.getParentId()) {
                mainInfoList.add(sourceInfoPO);
                knowledgeSourceIdSet.add(sourceInfoPO.getKnowledgeSourceId());
            } else {
                subInfoList.add(sourceInfoPO);
            }
        }

        if (knowledgeSourceIdSet.isEmpty()) {
            log.warn("三方资源Id为空，数据不正常，请排查数据");
            //暂时没有资源生成,直接返回
            return result;
        }
        //组内子资源map
        Map<Long, List<BookKnowledgeResourceCompleteInfoPO>> subInfoMap = new HashMap<>();
        if (!subInfoList.isEmpty()) {
            //子资源map数据
            subInfoMap =
                    subInfoList.stream().collect(Collectors.groupingBy(BookKnowledgeResourceCompleteInfoPO::getParentId));
        }

        //三方获取知识资源信息
        Map<String, KnowledgeResourceDetailResponse> resourceListDetailMap = getKnowledgeResourceDetailResponseMap(knowledgeSourceIdSet);

        //组装返回值
        for (BookKnowledgeResourceCompleteInfoPO sourceInfoPO : mainInfoList) {
            Long lastUpdateUserId = null;
            Long lastUpdateTime = null;
            //云知声返回的图谱资源Id
            String knowledgeSourceId = sourceInfoPO.getKnowledgeSourceId();
            if (!resourceListDetailMap.containsKey(knowledgeSourceId)) {
                log.warn("knowledgeSourceId is not exist in third platform:{},courseKnowledgeId:{}", knowledgeSourceId, sourceInfoPO.getId());
                continue;
            }
            KnowledgeResourceDetailResponse resourceDetailResponse = resourceListDetailMap.get(knowledgeSourceId);

            BookKnowledgeSourceInfoDTO sourceInfoDTO = new BookKnowledgeSourceInfoDTO();
            sourceInfoDTO.setId(sourceInfoPO.getId());
            sourceInfoDTO.setCourseKnowledgeId(sourceInfoPO.getBookKnowledgeId());
            sourceInfoDTO.setCourseResourceId(sourceInfoPO.getResourceId());
            sourceInfoDTO.setThirdResourceId(resourceDetailResponse.getId());
            sourceInfoDTO.setName(resourceDetailResponse.getName());
            sourceInfoDTO.setType(sourceInfoPO.getType() + "");
            sourceInfoDTO.setUrl(resourceDetailResponse.getUrl());
            sourceInfoDTO.setDir(resourceDetailResponse.getDir());
            sourceInfoDTO.setDescription(resourceDetailResponse.getDescription());
            sourceInfoDTO.setNodes(resourceDetailResponse.getNodes());
            sourceInfoDTO.setLabels(resourceDetailResponse.getLabels());
            sourceInfoDTO.setMainSourceUrl(sourceInfoPO.getSourceUrl());
            sourceInfoDTO.setEnableStatus(sourceInfoPO.getEnable());
            sourceInfoDTO.setStartTime(sourceInfoPO.getStartTime());
            sourceInfoDTO.setStartPictureUrl(sourceInfoPO.getStartPictureUrl());
            sourceInfoDTO.setCreateTime(sourceInfoPO.getCreateTime().getTime());
            sourceInfoDTO.setMultimediaKey(sourceInfoPO.getMultimediaKey());
            sourceInfoDTO.setMultimediaIndex(sourceInfoPO.getMultimediaIndex());
            sourceInfoDTO.setMultimediaName(sourceInfoPO.getMultimediaName());
            sourceInfoDTO.setLocation(sourceInfoPO.getLocation());
            lastUpdateUserId = sourceInfoPO.getUpdateBy();
            lastUpdateTime = sourceInfoPO.getUpdateTime().getTime();

            //如果是组的话，组内资源也需要组装
            if (subInfoMap.containsKey(sourceInfoPO.getId())) {
                List<BookKnowledgeResourceCompleteInfoPO> groupInnerInfo = subInfoMap.get(sourceInfoPO.getId());
                List<BookKnowledgeSourceInfoDTO.SubSourceInfo> subSourceInfos = new ArrayList<>();
                sourceInfoDTO.setSubSourceList(subSourceInfos);
                for (BookKnowledgeResourceCompleteInfoPO innerInfo : groupInnerInfo) {
                    BookKnowledgeSourceInfoDTO.SubSourceInfo subSourceInfo = new BookKnowledgeSourceInfoDTO.SubSourceInfo();
                    subSourceInfo.setId(innerInfo.getId());
                    subSourceInfo.setSourceUrl(innerInfo.getSourceUrl());
                    subSourceInfo.setEnableStatus(innerInfo.getEnable());
                    if (innerInfo.getUpdateTime().getTime() > sourceInfoPO.getUpdateTime().getTime()) {
                        lastUpdateUserId = innerInfo.getUpdateBy();
                        lastUpdateTime = innerInfo.getUpdateTime().getTime();
                    }
                    subSourceInfos.add(subSourceInfo);
                }
            }

            //设置最后更新人
            UserInfo userInfo = userService.getUserInfo(lastUpdateUserId);
            sourceInfoDTO.setUserName(userInfo.getName());
            sourceInfoDTO.setAvatarUrl(userInfo.getAvatarUrl());
            sourceInfoDTO.setSsoId(userInfo.getSsoId());
            sourceInfoDTO.setUpdateTime(lastUpdateTime);
            //设置数据
            result.add(sourceInfoDTO);
        }

        return result;
    }


    /**
     * 组装资源数据数据
     *
     * @param param
     * @param detailInfoPOS
     * @return
     */
    private List<BookKnowledgeResourceCompleteInfoPO> getBookKnowledgeResourceCompleteInfoPOS(KnowledgeSourceQueryParam param, List<BookKnowledgeResourceDetailInfoPO> detailInfoPOS) {
        List<Long> resourceIds = detailInfoPOS.stream().map(BookKnowledgeResourceDetailInfoPO::getResourceId).distinct().collect(Collectors.toList());
        Map<Long, BookKnowledgeResourceDetailInfoPO> detailInfoPOMap = detailInfoPOS.stream().collect(Collectors.toMap(BookKnowledgeResourceDetailInfoPO::getResourceId, Function.identity(), (a, b) -> a));
        Map<Long, BookKnowledgeResourceInfoPO> resourceInfoPOMap = bookKnowledgeResourceInfoMapper.selectByPrimaryKeyList(resourceIds).stream().collect(Collectors.toMap(BookKnowledgeResourceInfoPO::getId, Function.identity()));
        Map<Long, BookKnowledgeResourceMediaInfoPO> mediaInfoPOMap = bookKnowledgeResourceMediaInfoMapper.selectByResourceIds(resourceIds).stream().collect(Collectors.toMap(BookKnowledgeResourceMediaInfoPO::getResourceDetailId, Function.identity()));
        Map<Long, BookKnowledgeResourceQuestionInfoPO> questionInfoPOMap = bookKnowledgeResourceQuestionInfoMapper.selectByResourceIds(resourceIds).stream().collect(Collectors.toMap(BookKnowledgeResourceQuestionInfoPO::getResourceDetailId, Function.identity()));

        List<BookKnowledgeResourceCompleteInfoPO> infoPOList = new ArrayList<>();
        Long parentId = null;
        for (BookKnowledgeResourceDetailInfoPO detailInfoPO : detailInfoPOS) {
            BookKnowledgeResourceCompleteInfoPO completeInfoPO = new BookKnowledgeResourceCompleteInfoPO();
            completeInfoPO.setId(detailInfoPO.getId());
            completeInfoPO.setResourceId(detailInfoPO.getResourceId());
            completeInfoPO.setMainStatus(resourceInfoPOMap.get(detailInfoPO.getResourceId()).getSourceUrl().equals(detailInfoPO.getSourceUrl()) ? KnowledgeSourceMainEnum.MAIN.getCode() : KnowledgeSourceMainEnum.SUB.getCode());
            if (Objects.equals(KnowledgeSourceMainEnum.MAIN.getCode(), completeInfoPO.getMainStatus())) {
                parentId = detailInfoPO.getId();
            } else {
                completeInfoPO.setParentId(parentId);
            }
            completeInfoPO.setBookId(detailInfoPO.getBookId());
            completeInfoPO.setChapterId(detailInfoPO.getChapterId());
            completeInfoPO.setType(detailInfoPO.getType());

            completeInfoPO.setBookKnowledgeId(param.getCourseKnowledgeId());
            if (KnowledgeResourceTypeEnum.TEXT.getCode() == detailInfoPO.getType()) {
                String sourceRemark = detailInfoPOMap.get(detailInfoPO.getResourceId()).getSourceRemark();
                String sourceUrl = resourceInfoPOMap.get(detailInfoPO.getResourceId()).getSourceUrl();
                completeInfoPO.setMultimediaKey(StringUtils.isEmpty(sourceRemark) ? sourceUrl : sourceRemark);
                completeInfoPO.setMultimediaIndex("0");
            }
            if (KnowledgeResourceTypeEnum.VIDEO.getCode() == detailInfoPO.getType()) {
                BookKnowledgeResourceMediaInfoPO mediaInfoPO = mediaInfoPOMap.get(detailInfoPO.getId());
                completeInfoPO.setMultimediaKey(mediaInfoPO.getMultimediaKey());
                completeInfoPO.setMultimediaIndex(mediaInfoPO.getMultimediaIndex());
                completeInfoPO.setMultimediaName(mediaInfoPO.getMultimediaIndex());
                completeInfoPO.setStartTime(mediaInfoPOMap.get(detailInfoPO.getId()).getStartTime());
                completeInfoPO.setStartPictureUrl(mediaInfoPOMap.get(detailInfoPO.getId()).getStartPictureUrl());
                completeInfoPO.setEndTime(mediaInfoPOMap.get(detailInfoPO.getId()).getEndTime());
            }
            if (KnowledgeResourceTypeEnum.QUESTION.getCode() == detailInfoPO.getType()) {
                BookKnowledgeResourceQuestionInfoPO questionInfoPO = questionInfoPOMap.get(detailInfoPO.getId());
                completeInfoPO.setMultimediaKey(questionInfoPO.getQuestionKey());
                completeInfoPO.setMultimediaIndex(questionInfoPO.getQuestionIndex());
                completeInfoPO.setMultimediaName(questionInfoPO.getQuestionId());
            }

            completeInfoPO.setSourceUrl(detailInfoPO.getSourceUrl());
            completeInfoPO.setKnowledgeId(param.getKnowledgeId());
            completeInfoPO.setKnowledgeSourceId(resourceInfoPOMap.get(detailInfoPO.getResourceId()).getKnowledgeResourceId());
            completeInfoPO.setEnable(detailInfoPO.getStatus());
            completeInfoPO.setCreateTime(detailInfoPO.getCreateTime());
            completeInfoPO.setUpdateTime(detailInfoPO.getUpdateTime());
            completeInfoPO.setCreateBy(detailInfoPO.getCreateBy());
            completeInfoPO.setUpdateBy(detailInfoPO.getUpdateBy());
            completeInfoPO.setLocation(detailInfoPO.getLocation());
            infoPOList.add(completeInfoPO);
        }

        if (StringUtils.isNotBlank(param.getMultimediaKey())) {
            infoPOList = infoPOList.stream().filter(item -> item.getMultimediaKey().equals(param.getMultimediaKey())).collect(Collectors.toList());
        }
        return infoPOList;
    }

    @Override
    public List<BookKnowledgeSourceInfoGroupDTO> knowledgeResourceQueryGroup(KnowledgeSourceQueryGroupParam param, Long opUserId) {
        List<BookKnowledgeSourceInfoGroupDTO> result = new ArrayList<>();

        KnowledgeSourceQueryParam queryParam = new KnowledgeSourceQueryParam();
        BeanUtils.copyProperties(param, queryParam);
        List<BookKnowledgeSourceInfoDTO> bookKnowledgeSourceInfoDTOS = this.knowledgeResourceQuery(queryParam, opUserId);
        Map<String, List<BookKnowledgeSourceInfoDTO>> groupInfoByTypeMap = bookKnowledgeSourceInfoDTOS.stream().collect(Collectors.groupingBy(BookKnowledgeSourceInfoDTO::getMultimediaKey));
        if (CollectionUtils.isEmpty(groupInfoByTypeMap)) {
            return result;
        }

        groupInfoByTypeMap.forEach((multimediaKey, groupInfoList) -> {
            List<BookKnowledgeSourceInfoDTO> sortedList = groupInfoList.stream().sorted(Comparator.comparing(BookKnowledgeSourceInfoDTO::getMultimediaIndex)).collect(Collectors.toList());
            BookKnowledgeSourceInfoDTO lastInfo = sortedList.stream().max(Comparator.comparing(BookKnowledgeSourceInfoDTO::getUpdateTime)).get();
            BookKnowledgeSourceInfoGroupDTO groupDTO = new BookKnowledgeSourceInfoGroupDTO();
            groupDTO.setUserName(lastInfo.getUserName());
            groupDTO.setAvatarUrl(lastInfo.getAvatarUrl());
            groupDTO.setCreateTime(lastInfo.getUpdateTime());
            groupDTO.setDir(lastInfo.getDir());
            groupDTO.setMultimediaKey(multimediaKey);
            groupDTO.setType(lastInfo.getType());
            groupDTO.setBookKnowledgeSourceInfoDTOS(sortedList);
            groupDTO.setLocation(lastInfo.getLocation());
            result.add(groupDTO);
        });

        //根据文章的绝对的位置，做最后的排序
        result.sort(Comparator.comparing(BookKnowledgeSourceInfoGroupDTO::getLocation));

        return result;
    }

    @Override
    public BookKnowledgeResourceInfoPO courseChapterInfoGet(String bookId, String path) {
        return bookKnowledgeResourceInfoMapper.selectByBookIdAndSourceUrl(bookId, path);
    }

    private Map<String, KnowledgeResourceDetailResponse> getKnowledgeResourceDetailResponseMap(Set<String> knowledgeSourceIdSet) {
        Map<String, KnowledgeResourceDetailResponse> resourceListDetailMap = new HashMap<>();
        try {
            KnowledgeResourceListDetailRequest resourceListDetailRequest = new KnowledgeResourceListDetailRequest();
            resourceListDetailRequest.setIds(new ArrayList<>(knowledgeSourceIdSet));
            log.info("third request : {}", JSON.toJSONString(resourceListDetailRequest));
            BaseKnowledgeResponse<Map<String, KnowledgeResourceDetailResponse>> resourceListDetailResponse = knowledgeApiService.getResourceListDetail(resourceListDetailRequest);
            if (null != resourceListDetailResponse) {
                return resourceListDetailResponse.getResult();
            }
        } catch (Exception e) {
            log.error("getKnowledgeResourceDetailResponseMap error", e);
        }

        return resourceListDetailMap;
    }

    /**
     * @param pendingUrls
     * @param bookId
     * @param chapterId
     * @param type
     * @param bookKnowledgeId
     * @param knowledgeId
     */
    private void checkUrlsExistThenThrow(List<String> pendingUrls,
                                         String bookId,
                                         String chapterId,
                                         Integer type,
                                         Long bookKnowledgeId,
                                         String knowledgeId,
                                         List<Long> ignoreIds) {
        if (CollectionUtils.isEmpty(pendingUrls)) {
            throw new KnowledgeResourceUrlNullException();
        }

        BookKnowledgeSourceInfoExistPO selectExistParams = new BookKnowledgeSourceInfoExistPO();
        selectExistParams.setBookId(bookId);
        selectExistParams.setChapterId(chapterId);
        selectExistParams.setType(type);
        selectExistParams.setBookKnowledgeId(bookKnowledgeId);
        selectExistParams.setKnowledgeId(knowledgeId);
        selectExistParams.setSourceUrlList(pendingUrls);
        if (!CollectionUtils.isEmpty(ignoreIds)) {
            selectExistParams.setIgnoreIds(ignoreIds);
        }
        List<BookKnowledgeResourceCompleteInfoPO> existSourceUrls = bookKnowledgeSourceInfoMapper.selectExistSourceUrls(selectExistParams);
        if (!CollectionUtils.isEmpty(existSourceUrls)) {
            throw new KnowledgeResourceExistException(existSourceUrls.getFirst().getMultimediaKey());
        }
    }

    /**
     * @param pendingUrls
     * @param bookId
     * @param chapterId
     * @param knowledgeId
     * @param type
     * @param resourceId
     */
    private void checkUrlsNewExistThenThrow(List<String> pendingUrls,
                                            String bookId,
                                            String chapterId,
                                            String knowledgeId,
                                            Integer type,
                                            Long resourceId,
                                            List<Long> ignoreIds) {
        if (CollectionUtils.isEmpty(pendingUrls)) {
            throw new KnowledgeResourceUrlNullException();
        }

        BookKnowledgeSourceInfoExistPO selectExistParams = new BookKnowledgeSourceInfoExistPO();
        selectExistParams.setBookId(bookId);
        selectExistParams.setChapterId(chapterId);
        selectExistParams.setType(type);
        selectExistParams.setKnowledgeId(knowledgeId);
        selectExistParams.setBookKnowledgeId(resourceId);
        selectExistParams.setSourceUrlList(pendingUrls);
        if (!CollectionUtils.isEmpty(ignoreIds)) {
            selectExistParams.setIgnoreIds(ignoreIds);
        }
        List<BookKnowledgeResourceDetailInfoPO> existSourceUrls = bookKnowledgeResourceDetailInfoMapper.selectExistSourceUrls(selectExistParams);
        if (!CollectionUtils.isEmpty(existSourceUrls)) {
            throw new KnowledgeResourceExistException(existSourceUrls.getFirst().getSourceUrl());
        }
    }

    /**
     * @param pendingUrls
     * @param bookId
     * @param chapterId
     * @param type
     * @param bookKnowledgeId
     * @param knowledgeId
     */
    private List<BookKnowledgeResourceCompleteInfoPO> checkUrlsExistThenReturnInfos(List<String> pendingUrls,
                                                                                    String bookId,
                                                                                    String chapterId,
                                                                                    Integer type,
                                                                                    Long bookKnowledgeId,
                                                                                    String knowledgeId,
                                                                                    List<Long> ignoreIds) {
        if (CollectionUtils.isEmpty(pendingUrls)) {
            throw new KnowledgeResourceUrlNullException();
        }


        BookKnowledgeSourceInfoExistPO selectExistParams = new BookKnowledgeSourceInfoExistPO();
        selectExistParams.setBookId(bookId);
        selectExistParams.setChapterId(chapterId);
        selectExistParams.setType(type);
        selectExistParams.setBookKnowledgeId(bookKnowledgeId);
        selectExistParams.setKnowledgeId(knowledgeId);
        selectExistParams.setSourceUrlList(pendingUrls);
        if (!CollectionUtils.isEmpty(ignoreIds)) {
            selectExistParams.setIgnoreIds(ignoreIds);
        }
        List<BookKnowledgeResourceCompleteInfoPO> existSourceUrls = bookKnowledgeSourceInfoMapper.selectExistSourceUrls(selectExistParams);
        return CollectionUtils.isEmpty(existSourceUrls) ? new ArrayList<>() : existSourceUrls;
    }

    /**
     * @param pendingUrls
     * @param bookId
     * @param chapterId
     * @param knowledgeId
     * @param type
     * @param bookKnowledgeId
     */
    private List<BookKnowledgeResourceDetailInfoPO> checkUrlsNewExistThenReturnInfos(List<String> pendingUrls,
                                                                                     String bookId,
                                                                                     String chapterId,
                                                                                     String knowledgeId,
                                                                                     Integer type,
                                                                                     Long bookKnowledgeId,
                                                                                     List<Long> ignoreIds) {
        if (CollectionUtils.isEmpty(pendingUrls)) {
            throw new KnowledgeResourceUrlNullException();
        }


        BookKnowledgeSourceInfoExistPO selectExistParams = new BookKnowledgeSourceInfoExistPO();
        selectExistParams.setBookId(bookId);
        selectExistParams.setChapterId(chapterId);
        selectExistParams.setKnowledgeId(knowledgeId);
        selectExistParams.setType(type);
        selectExistParams.setBookKnowledgeId(bookKnowledgeId);
        selectExistParams.setSourceUrlList(pendingUrls);
        if (!CollectionUtils.isEmpty(ignoreIds)) {
            selectExistParams.setIgnoreIds(ignoreIds);
        }
        List<BookKnowledgeResourceDetailInfoPO> existSourceUrls = bookKnowledgeResourceDetailInfoMapper.selectExistSourceUrls(selectExistParams);
        return CollectionUtils.isEmpty(existSourceUrls) ? new ArrayList<>() : existSourceUrls;
    }


    /**
     * 所有信息全部失效
     *
     * @param resourceId
     * @param opUserId
     */
    private void resourceAllInfoDisableByResourceId(Long resourceId, Long opUserId, Integer resourceType) {
        BookKnowledgeResourceInfoPO resourceInfoPO = new BookKnowledgeResourceInfoPO();
        resourceInfoPO.setId(resourceId);
        resourceInfoPO.setEnable(KnowledgeResourceEnableEnum.DISABLE.getCode());
        resourceInfoPO.setUpdateBy(opUserId);
        resourceInfoPO.setUpdateTime(new Date());
        bookKnowledgeResourceInfoMapper.updateByPrimaryKeySelective(resourceInfoPO);

        BookKnowledgeResourceDetailInfoPO detailInfoPO = new BookKnowledgeResourceDetailInfoPO();
        detailInfoPO.setResourceId(resourceId);
        detailInfoPO.setEnable(KnowledgeResourceEnableEnum.DISABLE.getCode());
        detailInfoPO.setUpdateBy(opUserId);
        detailInfoPO.setUpdateTime(new Date());
        bookKnowledgeResourceDetailInfoMapper.updateByResourceIdSelective(detailInfoPO);

        //视频的内容修改
        if (Objects.equals(KnowledgeResourceTypeEnum.VIDEO.getCode(), resourceType)) {
            BookKnowledgeResourceMediaInfoPO mediaInfoPO = new BookKnowledgeResourceMediaInfoPO();
            mediaInfoPO.setResourceId(resourceId);
            mediaInfoPO.setEnable(KnowledgeResourceEnableEnum.DISABLE.getCode());
            mediaInfoPO.setUpdateBy(opUserId);
            mediaInfoPO.setUpdateTime(new Date());
            bookKnowledgeResourceMediaInfoMapper.updateByResourceIdSelective(mediaInfoPO);
        }

        //题目的内容修改
        if (Objects.equals(KnowledgeResourceTypeEnum.QUESTION.getCode(), resourceType)) {
            BookKnowledgeResourceQuestionInfoPO questionInfoPO = new BookKnowledgeResourceQuestionInfoPO();
            questionInfoPO.setResourceId(resourceId);
            questionInfoPO.setEnable(KnowledgeResourceEnableEnum.DISABLE.getCode());
            questionInfoPO.setUpdateBy(opUserId);
            questionInfoPO.setUpdateTime(new Date());
            bookKnowledgeResourceQuestionInfoMapper.updateByResourceIdSelective(questionInfoPO);
        }
    }
}
